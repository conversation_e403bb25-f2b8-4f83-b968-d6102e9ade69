# نظام إعادة تحديث Cache للوحة الإدارة

## نظرة عامة

تم إنشاء نظام شامل لإعادة تحديث cache الإحصائيات في الصفحة الرئيسية للوحة الإدارة (`/admin`) عند إضافة أو تحديث أو حذف أي عنصر يؤثر على الإحصائيات المعروضة.

## كيفية العمل

### 1. الصفحة الرئيسية للإدارة
- تستخدم `unstable_cache` مع tag: `"admin-dashboard-stats"`
- تعيد التحقق كل 5 دقائق (`revalidate: 300`)
- تجلب جميع الإحصائيات في استعلام واحد باستخدام `Promise.all`

### 2. دوال إعادة التحديث
ملف: `src/lib/cache-invalidation.ts`

```typescript
// الدالة الرئيسية
export async function revalidateAdminDashboard() {
  revalidateTag("admin-dashboard-stats");
}

// دوال مخصصة لكل نوع
export async function revalidateUsers() { ... }
export async function revalidateCourses() { ... }
export async function revalidateArticles() { ... }
// ... إلخ
```

### 3. التطبيق في العمليات الإدارية

تم إضافة استدعاءات `revalidate*()` في الملفات التالية:

#### المقالات
- `src/app/(dashboard)/admin/articles/page.tsx` (حذف)
- `src/app/(dashboard)/admin/articles/create/page.tsx` (إنشاء)
- `src/app/(dashboard)/admin/articles/update/[articleId]/page.tsx` (تحديث)

#### الكورسات
- `src/app/(dashboard)/admin/courses/page.tsx` (حذف وتحديث)
- `src/app/(dashboard)/admin/courses/create/page.tsx` (إنشاء)
- `src/app/(dashboard)/admin/courses/update/[id]/page.tsx` (تحديث)

#### الكتب
- `src/app/(dashboard)/admin/books/page.tsx` (حذف)
- `src/app/(dashboard)/admin/books/create/page.tsx` (إنشاء)
- `src/app/(dashboard)/admin/books/update/[bookId]/page.tsx` (تحديث)

#### المقابلات
- `src/app/(dashboard)/admin/interviews/page.tsx` (حذف)
- `src/app/(dashboard)/admin/interviews/create/page.tsx` (إنشاء)
- `src/app/(dashboard)/admin/interviews/update/[interviewId]/page.tsx` (تحديث)

#### منشورات المدونة
- `src/app/(dashboard)/admin/blogPosts/page.tsx` (حذف)
- `src/app/(dashboard)/admin/blogPosts/create/page.tsx` (إنشاء)
- `src/app/(dashboard)/admin/blogPosts/update/[postId]/page.tsx` (تحديث)

#### الاستشارات
- `src/components/dashboard-workspace/consultations-CRUD/db-queries.ts`
  - `markConsultationAsRead()` (تحديث)
  - `deleteConsultation()` (حذف)

#### شهادات العملاء
- `src/app/(dashboard)/admin/testimonys/page.tsx` (حذف)
- `src/app/(dashboard)/admin/testimonys/create/page.tsx` (إنشاء)
- `src/app/(dashboard)/admin/testimonys/update/[testimonyId]/page.tsx` (تحديث)

#### المشرفين
- `src/app/(dashboard)/admin/admins-management/page.tsx` (حذف)
- `src/app/(dashboard)/admin/admins-management/create/page.tsx` (إنشاء)
- `src/app/(dashboard)/admin/admins-management/update/[adminId]/page.tsx` (تحديث)

## الملفات المتبقية للتحديث

يجب إضافة استدعاءات `revalidate*()` في الملفات التالية:

### إنشاء العناصر
- `src/app/(dashboard)/admin/books/create/page.tsx`
- `src/app/(dashboard)/admin/interviews/create/page.tsx`
- `src/app/(dashboard)/admin/testimonys/create/page.tsx`
- `src/app/(dashboard)/admin/admins-management/create/page.tsx`

### تحديث العناصر
- `src/app/(dashboard)/admin/articles/update/[articleId]/page.tsx`
- `src/app/(dashboard)/admin/books/update/[bookId]/page.tsx`
- `src/app/(dashboard)/admin/interviews/update/[interviewId]/page.tsx`
- `src/app/(dashboard)/admin/blogPosts/update/[postId]/page.tsx`
- `src/app/(dashboard)/admin/testimonys/update/[testimonyId]/page.tsx`
- `src/app/(dashboard)/admin/admins-management/update/[adminId]/page.tsx`

### المحاضرات (تؤثر على عدد الكورسات)
- `src/app/(dashboard)/admin/courses/lectures/[courseId]/create/page.tsx`
- `src/app/(dashboard)/admin/courses/lectures/[courseId]/update/[lectureId]/page.tsx`
- `src/app/(dashboard)/admin/courses/lectures/[courseId]/page.tsx`

### المستخدمين (عند التسجيل)
- `src/app/(website)/auth/signup/signup-action.ts`

## مثال على الاستخدام

```typescript
// في أي عملية إدارية
import { revalidateArticles } from "@/lib/cache-invalidation";

export async function deleteArticle(id: string) {
  await prisma.article.delete({ where: { id } });
  
  // إعادة تحديث cache المقالات والإحصائيات
  revalidatePath("/admin/articles");
  await revalidateArticles(); // هذا سيحدث cache الإحصائيات
  
  return { success: true };
}
```

## الفوائد

1. **تحديث فوري**: الإحصائيات تتحدث فوراً بعد أي عملية
2. **أداء محسن**: استخدام cache مع إعادة تحديث ذكية
3. **سهولة الصيانة**: دوال مركزية لإدارة cache
4. **شمولية**: تغطي جميع العمليات الإدارية

## ملاحظات مهمة

- يجب استدعاء `await revalidate*()` في جميع العمليات الإدارية
- الدوال async لذا يجب استخدام `await`
- يمكن إضافة المزيد من tags للتحكم الدقيق في cache
