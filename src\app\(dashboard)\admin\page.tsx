import {
  BookOpen,
  Calendar,
  FileText,
  GraduationCap,
  Mail,
  MessageSquare,
  PenTool,
  TrendingUp,
  Users,
  Video,
} from "lucide-react"

import { unstable_cache } from "next/cache"

import prisma from "@/lib/prisma"
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card"
import { getAdminSession } from "@/components/dashboard/components/auth/server-actions"
import { getConsultationUnReadCountCache } from "@/components/dashboard-workspace/consultations-CRUD/db-queries"

// دالة لجلب الإحصائيات مع cache
const getDashboardStats = unstable_cache(
  async () => {
    const [
      usersCount,
      coursesCount,
      articlesCount,
      booksCount,
      interviewsCount,
      blogPostsCount,
      consultationsCount,
      unreadConsultationsCount,
      testimonialsCount,
      adminsCount,
      recentUsers,
      recentConsultations,
    ] = await Promise.all([
      prisma.user.count(),
      prisma.course.count(),
      prisma.article.count(),
      prisma.book.count(),
      prisma.interview.count(),
      prisma.blogPost.count(),
      prisma.consultation.count(),
      getConsultationUnReadCountCache(),
      prisma.testimony.count(),
      prisma.admin.count(),
      prisma.user.findMany({
        take: 5,
        orderBy: { id: "desc" },
        select: { name: true, email: true, emailVerified: true, id: true },
      }),
      prisma.consultation.findMany({
        take: 5,
        orderBy: { createdAt: "desc" },
        select: {
          name: true,
          email: true,
          message: true,
          read: true,
          createdAt: true,
          id: true,
        },
      }),
    ])

    return {
      usersCount,
      coursesCount,
      articlesCount,
      booksCount,
      interviewsCount,
      blogPostsCount,
      consultationsCount,
      unreadConsultationsCount,
      testimonialsCount,
      adminsCount,
      recentUsers,
      recentConsultations,
    }
  },
  ["admin-dashboard-stats"],
  {
    tags: ["admin-dashboard-stats"],
    revalidate: 300, // إعادة التحقق كل 5 دقائق
  }
)

export default async function AdminPage() {
  const adminSession = await getAdminSession()
  const stats = await getDashboardStats()

  const statsCards = [
    {
      title: "المستخدمون",
      value: stats.usersCount,
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50 dark:bg-blue-950/20",
    },
    {
      title: "الكورسات",
      value: stats.coursesCount,
      icon: GraduationCap,
      color: "text-green-600",
      bgColor: "bg-green-50 dark:bg-green-950/20",
    },
    {
      title: "المقالات",
      value: stats.articlesCount,
      icon: FileText,
      color: "text-purple-600",
      bgColor: "bg-purple-50 dark:bg-purple-950/20",
    },
    {
      title: "الكتب",
      value: stats.booksCount,
      icon: BookOpen,
      color: "text-orange-600",
      bgColor: "bg-orange-50 dark:bg-orange-950/20",
    },
    {
      title: "المقابلات",
      value: stats.interviewsCount,
      icon: Video,
      color: "text-red-600",
      bgColor: "bg-red-50 dark:bg-red-950/20",
    },
    {
      title: "منشورات المدونة",
      value: stats.blogPostsCount,
      icon: PenTool,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50 dark:bg-indigo-950/20",
    },
    {
      title: "الاستشارات",
      value: stats.consultationsCount,
      icon: MessageSquare,
      color: "text-teal-600",
      bgColor: "bg-teal-50 dark:bg-teal-950/20",
    },
    {
      title: "الاستشارات غير المقروءة",
      value: stats.unreadConsultationsCount,
      icon: Mail,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50 dark:bg-yellow-950/20",
    },
  ]

  return (
    <div className="space-y-6 p-6">
      {/* ترحيب */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          مرحباً، {adminSession?.name}
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          إليك نظرة عامة على إحصائيات الموقع والأنشطة الحديثة
        </p>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {statsCards.map((stat, index) => {
          const IconComponent = stat.icon
          return (
            <Card key={index} className="transition-shadow hover:shadow-md">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.title}
                </CardTitle>
                <div className={`rounded-lg p-2 ${stat.bgColor}`}>
                  <IconComponent className={`h-5 w-5 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {stat.value.toLocaleString("ar-SA")}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* الأنشطة الحديثة */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* المستخدمون الجدد */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              المستخدمون الجدد
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentUsers.length > 0 ? (
                stats.recentUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between rounded-lg bg-gray-50 p-3 dark:bg-gray-800"
                  >
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {user.name}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {user.email}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {user.emailVerified ? (
                        <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900 dark:text-green-200">
                          مفعل
                        </span>
                      ) : (
                        <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                          غير مفعل
                        </span>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <p className="py-4 text-center text-gray-500 dark:text-gray-400">
                  لا توجد مستخدمون جدد
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* الاستشارات الحديثة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-teal-600" />
              الاستشارات الحديثة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentConsultations.length > 0 ? (
                stats.recentConsultations.map((consultation) => (
                  <div
                    key={consultation.id}
                    className="rounded-lg bg-gray-50 p-3 dark:bg-gray-800"
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {consultation.name}
                      </p>
                      <div className="flex items-center gap-2">
                        {!consultation.read && (
                          <span className="h-2 w-2 rounded-full bg-red-500"></span>
                        )}
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(consultation.createdAt).toLocaleDateString(
                            "ar-SA"
                          )}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {consultation.email}
                    </p>
                    <p className="mt-1 line-clamp-2 text-sm text-gray-700 dark:text-gray-300">
                      {consultation.message}
                    </p>
                  </div>
                ))
              ) : (
                <p className="py-4 text-center text-gray-500 dark:text-gray-400">
                  لا توجد استشارات حديثة
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* معلومات إضافية */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              شهادات العملاء
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {stats.testimonialsCount.toLocaleString("ar-SA")}
            </div>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              الحد الأقصى: 3 شهادات
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-purple-600" />
              المشرفون
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {stats.adminsCount.toLocaleString("ar-SA")}
            </div>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              إجمالي المشرفين
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              تاريخ اليوم
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {new Date().toLocaleDateString("ar-SA", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
